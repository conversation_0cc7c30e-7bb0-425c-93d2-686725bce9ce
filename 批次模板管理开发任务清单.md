# 批次模板管理 (templateManagement) 开发任务清单

根据融合平台迁移规则，对批次模板管理模块进行代码迁移和优化。

## 任务概述

**模块路径**: `projects/piem/src/projects/templateManagement/`
**URL 路径**: `/#/templateManagement`
**涉及文件**:

- `index.vue` - 主页面组件
- `addOrEdit.vue` - 新增/编辑模板弹窗组件
- `detailDrawer.vue` - 模板详情抽屉组件
- `assets/` - 静态资源文件夹

---

## 详细任务清单

**序号**: 1
**文件名称**: `projects/piem/src/projects/templateManagement/index.vue`
**行号**: 46
**问题类型**: 引用路径修改
**描述**: 将 `import common from "eem-utils/common";` 修改为 `import common from "eem-base/utils/common";`

---

**序号**: 2
**文件名称**: `projects/piem/src/projects/templateManagement/index.vue`
**行号**: 12
**问题类型**: CSS 类名修改
**描述**: 将 `class="p0 mtJ3"` 修改为 `class="p0 mt-J4"`，根据规则 J3->J4

---

**序号**: 3
**文件名称**: `projects/piem/src/projects/templateManagement/index.vue`
**行号**: 30
**问题类型**: CSS 类名修改
**描述**: 将 `class="handle mlJ3"` 修改为 `class="handle ml-J0"`，根据规则 J->J0

---

**序号**: 4
**文件名称**: `projects/piem/src/projects/templateManagement/index.vue`
**行号**: 33
**问题类型**: CSS 类名修改
**描述**: 将 `class="handle del mlJ3"` 修改为 `class="handle del ml-J0"`，根据规则 J->J0

---

**序号**: 5
**文件名称**: `projects/piem/src/projects/templateManagement/index.vue`
**行号**: 214-217
**问题类型**: SCSS 样式修改
**描述**: 将 `@include padding(J4);` `@include background_color(BG1);` `@include border_radius(C);` 修改为对应的 CSS 变量

---

**序号**: 6
**文件名称**: `projects/piem/src/projects/templateManagement/index.vue`
**行号**: 221
**问题类型**: SCSS 样式修改
**描述**: 将 `@include font_color(ZS);` 修改为对应的 CSS 变量

---

**序号**: 7
**文件名称**: `projects/piem/src/projects/templateManagement/index.vue`
**行号**: 224
**问题类型**: SCSS 样式修改
**描述**: 将 `@include font_color(Sta3);` 修改为对应的 CSS 变量

---

**序号**: 8
**文件名称**: `projects/piem/src/projects/templateManagement/addOrEdit.vue`
**行号**: 131
**问题类型**: 引用路径修改
**描述**: 将 `import common from "eem-utils/common";` 修改为 `import common from "eem-base/utils/common";`

---

**序号**: 9
**文件名称**: `projects/piem/src/projects/templateManagement/addOrEdit.vue`
**行号**: 10
**问题类型**: CSS 变量修改
**描述**: 将 `:gutter="$J3"` 修改为 `:gutter="$J4"`，根据规则 J3->J4

---

**序号**: 10
**文件名称**: `projects/piem/src/projects/templateManagement/addOrEdit.vue`
**行号**: 109
**问题类型**: CSS 类名修改
**描述**: 将 `class="icon delete mlJ3"` 修改为 `class="icon delete ml-J0"`，根据规则 J->J0

---

**序号**: 11
**文件名称**: `projects/piem/src/projects/templateManagement/addOrEdit.vue`
**行号**: 415-417
**问题类型**: SCSS 样式修改
**描述**: 将 `@include background_color(BG);` `@include padding(J1);` 修改为对应的 CSS 变量

---

**序号**: 12
**文件名称**: `projects/piem/src/projects/templateManagement/addOrEdit.vue`
**行号**: 422
**问题类型**: SCSS 样式修改
**描述**: 将 `@include font_color(Sta3);` 修改为对应的 CSS 变量

---

**序号**: 13
**文件名称**: `projects/piem/src/projects/templateManagement/detailDrawer.vue`
**行号**: 6
**问题类型**: CSS 类名修改
**描述**: 将 `class="eem-cont-c1 flex-auto flex-column"` 修改为 `class="eem-cont-c1 flex flex-auto flex flex-col"`，根据规则 flex-column->flex flex-col

---

**序号**: 14
**文件名称**: `projects/piem/src/projects/templateManagement/detailDrawer.vue`
**行号**: 12
**问题类型**: CSS 类名修改
**描述**: 将 `class="mbJ3"` 修改为 `class="mb-J4"`，根据规则 J3->J4

---

**序号**: 15
**文件名称**: `projects/piem/src/projects/templateManagement/detailDrawer.vue`
**行号**: 14
**问题类型**: CSS 类名修改
**描述**: 将 `class="fct3 mt0 mbJ"` 修改为 `class="fct3 mt0 mb-J0"`，根据规则 J->J0

---

**序号**: 16
**文件名称**: `projects/piem/src/projects/templateManagement/detailDrawer.vue`
**行号**: 135
**问题类型**: SCSS 样式修改
**描述**: 将 `@include background_color(BG);` 修改为对应的 CSS 变量

---

**序号**: 17
**文件名称**: `projects/piem/src/projects/templateManagement/detailDrawer.vue`
**行号**: 139
**问题类型**: SCSS 样式修改
**描述**: 将 `@include background_color(BG1);` 修改为对应的 CSS 变量

---

**序号**: 18
**文件名称**: `projects/piem/src/projects/templateManagement/detailDrawer.vue`
**行号**: 142
**问题类型**: SCSS 样式修改
**描述**: 将 `@include margin(J1);` 修改为对应的 CSS 变量

---

**序号**: 19
**文件名称**: `projects/piem/src/projects/templateManagement/detailDrawer.vue`
**行号**: 149
**问题类型**: SCSS 样式修改
**描述**: 将 `@include font_color(T3);` 修改为对应的 CSS 变量

---

**序号**: 20
**文件名称**: `projects/piem/src/api/piem/batchEnergyEfficiency.js`
**行号**: 1
**问题类型**: 引用路径修改
**描述**: 将 `import fetch from "eem-utils/fetch";` 修改为 `import fetch from "eem-base/utils/fetch";`

---

**序号**: 21
**文件名称**: `projects/piem/src/api/piem/common.js`
**行号**: 16
**问题类型**: API 文件引用
**描述**: 确保 batchEnergyEfficiency.js 在 common.js 中正确导出，供 custom.js 使用

---
