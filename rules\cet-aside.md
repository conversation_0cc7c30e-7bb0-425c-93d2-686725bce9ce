# cet-aside 组件使用规则

## 1. 组件概述

`cet-aside` 是一个侧边栏布局组件，提供左侧可折叠的侧边栏和右侧主内容区域。组件内置折叠/展开功能，支持响应式布局。

## 2. 插槽 (Slots)

| 插槽名      | 说明           | 默认值 |
| ----------- | -------------- | ------ |
| `aside`     | 左侧侧边栏内容 | -      |
| `container` | 右侧主内容区域 | -      |

## 3. 事件 (Events)

| 事件名     | 说明                     | 参数                       |
| ---------- | ------------------------ | -------------------------- |
| `collapse` | 侧边栏折叠状态改变时触发 | `{ isCollapsed: boolean }` |

## 4. 样式特性

- **侧边栏宽度**：默认 300px，折叠后为 10px
- **过渡动画**：0.3s 平滑过渡效果
- **折叠按钮**：自动居中显示，支持 hover 效果
- **响应式设计**：右侧内容区自动填充剩余空间

## 5. 使用示例

### 5.1 基础用法（左侧节点树）

```vue
<template>
  <cet-aside @collapse="handleCollapse">
    <template #aside>
      <div class="tree-sidebar">
        <CetGiantTree
          ref="orgTree"
          v-bind="CetGiantTree_org"
          v-on="CetGiantTree_org.event"
          :searchText_in.sync="CetGiantTree_org.searchText_in"
        />
      </div>
    </template>
    <template #container>
      <div class="main-content">
        <h2>{{ selectedNode ? selectedNode.name : "请选择节点" }}</h2>
        <div v-if="selectedNode" class="node-details">
          <p>
            <strong>节点ID：</strong>
            {{ selectedNode.tree_id }}
          </p>
          <p>
            <strong>节点名称：</strong>
            {{ selectedNode.name }}
          </p>
          <p>
            <strong>节点类型：</strong>
            {{ selectedNode.type }}
          </p>
          <p>
            <strong>描述：</strong>
            {{ selectedNode.description }}
          </p>
        </div>
        <div v-else class="empty-state">
          <p>请从左侧树结构中选择一个节点查看详情</p>
        </div>
      </div>
    </template>
  </cet-aside>
</template>

<script>
export default {
  data() {
    return {
      selectedNode: null,
      CetGiantTree_org: {
        inputData_in: [],
        checkedNodes: [],
        selectNode: {},
        setting: {
          check: {
            enable: false // 单选模式
          },
          data: {
            simpleData: {
              enable: false, // 使用复杂数据格式
              idKey: "tree_id" // 节点唯一标识字段
            },
            key: {
              name: "name"
            }
          }
        },
        searchText_in: "",
        event: {
          created_out: this.CetGiantTree_org_created_out,
          currentNode_out: this.CetGiantTree_org_currentNode_out
        }
      }
    };
  },
  methods: {
    handleCollapse(event) {
      console.log("侧边栏折叠状态:", event.isCollapsed);
    },
    CetGiantTree_org_created_out(ztreeObj) {
      this.ztreeObj = ztreeObj;
      console.log("组织架构树构建完成");
    },
    CetGiantTree_org_currentNode_out(node) {
      this.selectedNode = node;
      console.log("选中节点:", node);
    }
  }
};
</script>

<style scoped>
.tree-sidebar {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.node-details {
  background: var(--BG2);
  padding: 16px;
  border-radius: 8px;
  margin-top: 16px;
}

.empty-state {
  text-align: center;
  color: var(--T3);
  margin-top: 40px;
}
</style>
```

## 6. 注意事项

1. **容器高度**：确保父容器有明确的高度设置，组件会填充 100% 高度
2. **内容溢出**：侧边栏和主内容区都设置了 `overflow: hidden`，如需滚动请在内部元素添加
3. **样式定制**：组件使用 CSS 变量，可通过修改变量值进行主题定制

## 7. 常用样式

**将以下两种样式添加到公共 `src/resources/common.scss` 中**

### 7.1 Tab 页 + 左树右内容布局

适用于页面上方有 tab 页，下方为左树右内容的布局场景：

```scss
.tab-content-aside {
  .cet-content-aside-line {
    background-color: var(--B1);
  }
  .cet-content-aside-container {
    margin-left: 0 !important;
  }
  .cet-content-aside-sidebar {
    border-right: 1px solid var(--B1) !important;
    border-radius: 0 !important;
  }
}
```

**使用场景**：

- 页面顶部有标签页导航
- 需要与上方内容无缝连接
- 侧边栏需要扁平化边框样式

### 7.2 直接左树右内容布局

适用于直接展示左树右内容区域的布局场景：

```scss
.content-bg-aside {
  .cet-content-aside-container {
    background-color: var(--BG1);
    border-radius: 4px;
  }
}
```

**使用场景**：

- 独立的内容区域
- 需要背景色区分
- 圆角卡片式设计

### 7.3 使用示例

```vue
<template>
  <!-- Tab页 + 左树右内容布局 -->
  <div class="tab-content-aside">
    <cet-aside>
      <!-- 侧边栏和内容 -->
    </cet-aside>
  </div>

  <!-- 直接左树右内容布局 -->
  <div class="content-bg-aside">
    <cet-aside>
      <!-- 侧边栏和内容 -->
    </cet-aside>
  </div>
</template>
```

## 8. 样式规范

**禁止行为：**

- ❌ 不允许使用 `::v-deep`、`/deep/` 或直接类名覆盖组件内部样式
- ❌ 不允许强制修改侧边栏宽度、过渡时间等核心样式属性
- ❌ 不允许破坏组件的响应式布局结构

**推荐做法：**

- ✅ 通过外层容器设置整体布局样式
- ✅ 在插槽内容中添加自定义样式
- ✅ 使用 CSS 变量进行主题定制
- ✅ 使用上述两种常用样式类
- ✅ 如需特殊样式需求，联系组件维护者

**违规后果：**

- 可能导致组件功能异常或样式冲突
- 影响组件的可维护性和升级兼容性
- 代码审查时将被要求修改
