# 融合平台 API 使用规则

## 1. 概述

`@altair/knight` 是融合平台的核心 API 库，提供了插件与主应用之间的通信接口。该库支持两种运行模式：

- **融合模式**: 作为微前端插件在融合平台中运行
- **独立模式**: 作为独立应用运行

## 2. 安装和引入

```javascript
// 引入融合平台 API
import { api } from "@altair/knight";

// 或者按需引入
import { fusionApi } from "@altair/knight/api";
```

## 3. 核心 API

### 3.1 重要 API（高优先级）

#### getUser()

获取当前登录用户信息，返回 User 实例

```javascript
const user = api.getUser();
console.log("用户ID:", user.getUserId());
console.log("用户名:", user.getUserName());
console.log("租户ID:", user.getUserTenantId());
```

##### User 类方法

- `getUserId()`: 获取用户 ID
- `getUserName()`: 获取用户名
- `getUserTenantId()`: 获取租户 ID
- `getUserGraphNodes()`: 获取用户图形节点权限
- `getUserModelNodes()`: 获取用户模型节点权限
- `getUserCustomConfig()`: 获取用户自定义配置
- `getRole()`: 获取用户角色信息
- `getRoleId()`: 获取角色 ID
- `getRoleAuths()`: 获取角色权限
- `isSameUser(id)`: 判断是否为同一用户

#### checkPermission(name)

校验权限字段-包括操作权限和页面权限

```javascript
if (api.checkPermission("user_create")) {
  // 有权限时的操作
  console.log("有创建用户的权限");
}
if (api.checkPermission("base-config.userManage")) {
  // 有权限时的操作
  console.log("有用户管理页面的权限");
}
```

#### routerPushWithQuery({ path, query })

插件内跳转路由界面

```javascript
api.routerPushWithQuery({
  path: "/fusion/base-config/user",
  query: { id: 123, type: "edit" }
});
```

**注意**:

- 跳转页面必须使用该 API，不能使用 router.push 方法，且参数只能通过 query 传递
- 跳转页面前必须先判断页面是否存在，使用 `checkNavmenuExit` 方法

#### getRouterQuery()

获取主应用当前路由的 query 参数

```javascript
const query = api.getRouterQuery();
console.log("路由参数:", query);
```

#### checkNavmenuExit(menuItem)

检查某个菜单页面是否存在和加载，一般用于跳转页面前判断页面是否存在

```javascript
if (api.checkNavmenuExit("/fusion/base-config/user")) {
  console.log("用户配置页面存在");
}
```

### 3.2 权限管理

#### isRoot()

判断当前用户是否为 ROOT 用户

```javascript
if (api.isRoot()) {
  console.log("当前用户是ROOT用户");
}
```

### 3.3 路由管理

#### getCurrentRoutePath()

获取主应用当前的路由路径

```javascript
const currentPath = api.getCurrentRoutePath();
console.log("当前路由:", currentPath);
```

### 3.4 前端配置管理

#### getFrontendConfig()

获取当前子应用的前端配置

```javascript
const config = api.getFrontendConfig();
console.log("前端配置:", config);
```

#### setFrontendConfig(params)

设置当前子应用的前端配置

```javascript
api
  .setFrontendConfig({
    title: "自定义标题",
    theme: "dark"
  })
  .then(() => {
    console.log("配置设置成功");
  });
```

### 3.5 插件信息获取

#### getPluginName()

获取当前插件的名称

```javascript
const pluginName = api.getPluginName();
console.log("当前插件名称:", pluginName);
```

**注意**: 独立运行时不可调用

### 3.6 配置信息获取

#### getConf()

获取主应用的配置信息

```javascript
const config = api.getConf();
console.log("主应用配置:", config);
```

#### getHttpHeaders()

获取主应用 baseHttp 添加的请求头

```javascript
const headers = api.getHttpHeaders();
console.log("请求头:", headers);
// 融合模式示例输出: { "User-ID": 123, "X-Auth-Tenant": 456 }
// 独立模式输出: { "User-ID": 1, "X-Auth-Tenant": 2 }
```

#### getTenantId()

获取当前请求头使用的租户 ID

```javascript
const tenantId = api.getTenantId();
console.log("租户ID:", tenantId);
```

### 3.7 应用状态管理

#### getAppState()

获取所有插件信息

```javascript
const appState = api.getAppState();
console.log("插件状态:", appState);
```

#### checkAppExit(app)

检查某个插件是否存在和加载

```javascript
if (api.checkAppExit("user-management")) {
  console.log("用户管理插件已加载");
}
```

### 3.6 路由管理

#### getCurrentRoutePath()

获取主应用当前的路由路径

```javascript
const currentPath = api.getCurrentRoutePath();
console.log("当前路由:", currentPath);
```

### 3.8 项目模式管理

#### projectMode()

获取当前的用户状态

```javascript
const mode = api.projectMode();
console.log("项目模式:", mode);
// 可能的返回值:
// "PlatInProject" - 平台用户在项目中
// "PlatInPlat" - 平台用户在平台中
// "ProjectInProject" - 平台模式的项目用户在项目中
// "RealProject" - 独立运行模式
```

### 3.9 系统级操作

#### system(fn, data)

触发系统级别的事件

```javascript
api.system(
  ({ conf, router, store, bus }, data) => {
    // 访问主应用的配置、路由、状态管理和事件总线
    console.log("系统配置:", conf);
    router.push("/some-path");
  },
  { customData: "example" }
);
```

#### destroy(name)

销毁指定插件

```javascript
api.destroy("plugin-name");
```

### 3.10 打印功能

#### print(dom, title)

将特定元素输出为 PDF 文件

```javascript
const element = document.getElementById("print-content");
api.print(element, "报告标题");
```

### 3.11 项目图片管理

#### setProjectImg({ file, name }, iconFileName, projectId)

保存项目级别的图片

```javascript
const fileInput = document.getElementById("file-input");
const file = fileInput.files[0];

api.setProjectImg({ file, name: "custom-name" }, "logo", 123).then(key => {
  console.log("图片保存成功，key:", key);
});
```

#### getProjectImg(iconFileName, projectId)

获取项目级别的图片

```javascript
const imageUrl = api.getProjectImg("logo", 123);
console.log("图片URL:", imageUrl);
```

#### deleteProjectImg(iconFileName, projectId)

删除项目级别的图片

```javascript
api.deleteProjectImg("logo", 123);
```

### 3.12 其他功能

#### fullScreenNavTrigger()

大屏状态下切换菜单的显示状态

```javascript
api.fullScreenNavTrigger();
```

#### Unauthorized()

触发主应用的失去权限操作

```javascript
api.Unauthorized();
```

#### getFusionOptions()

获取融合主应用 fusion.js 中的 options 选项

```javascript
const options = api.getFusionOptions();
console.log("融合配置:", options);
```

#### checkProjectMenuEmpty(data)

查询项目的菜单配置是否存在

```javascript
api.checkProjectMenuEmpty([1, 2, 3]).then(result => {
  console.log("菜单配置检查结果:", result);
});
```

## 4. 最佳实践

### 4.1 环境检测

```javascript
// 检测运行环境
if (window.__POWERED_BY_WUJIE__) {
  console.log("运行在融合平台中");
} else {
  console.log("独立运行模式");
}
```

### 4.2 权限检查

```javascript
// 统一的权限检查函数
function hasPermission(permission) {
  try {
    return api.checkPermission(permission);
  } catch (error) {
    console.warn("权限检查失败:", error);
    return false;
  }
}

// 使用示例
if (hasPermission("user:create")) {
  // 显示创建按钮
}
```

### 4.3 配置管理

```javascript
// 获取配置的安全方法
function getConfigSafely(key, defaultValue = null) {
  try {
    const config = api.getFrontendConfig();
    return config[key] || defaultValue;
  } catch (error) {
    console.warn("获取配置失败:", error);
    return defaultValue;
  }
}

// 使用示例
const theme = getConfigSafely("theme", "light");
```

## 5. 注意事项

### 5.1 运行模式差异

- **融合模式**: 所有 API 功能完整可用
- **独立模式**: 部分 API 功能受限，会输出警告信息

### 5.2 权限管理

- 独立运行时，所有权限检查都返回 `true`
- 融合模式下，权限检查基于实际的用户权限配置

### 5.3 错误处理

- 在独立模式下调用不支持的 API 会输出 `console.warn` 警告
- 建议在调用 API 前进行环境检测

### 5.4 生命周期

- 确保在组件销毁时清理相关资源
- 避免在组件销毁后继续调用 API

### 5.5 性能考虑

- 避免频繁调用 `getUser()` 等重量级 API
- 建议在应用启动时缓存用户信息

## 6. 常见问题

### Q: 如何判断当前是否在融合平台中运行？

A: 检查 `window.__POWERED_BY_WUJIE__` 变量

### Q: 独立运行时如何模拟用户信息？

A: 独立模式下 `getUser()` 返回预设的 ROOT 用户信息

### Q: 权限指令不生效怎么办？

A: 确保正确安装了权限插件，并传入了 `checkPermission` 方法

### Q: 如何处理 API 调用失败？

A: 使用 try-catch 包装 API 调用，并提供降级方案
